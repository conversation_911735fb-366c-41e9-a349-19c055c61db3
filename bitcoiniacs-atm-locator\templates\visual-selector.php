<?php
/**
 * Visual Location Selector Template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}
?>

<div class="bitcoiniacs-visual-selector" id="<?php echo esc_attr($map_id); ?>-visual-selector">
  <div class="visual-selector-header">
    <h3><?php _e('Select Location Visually', 'bitcoiniacs-atm-locator'); ?></h3>
    <p><?php _e('Click on a region to explore ATM locations', 'bitcoiniacs-atm-locator'); ?></p>
  </div>
  
  <div class="visual-selector-tabs">
    <button class="selector-tab active" data-tab="country">
      <?php _e('Country', 'bitcoiniacs-atm-locator'); ?>
    </button>
    <button class="selector-tab disabled" data-tab="region">
      <?php _e('Region', 'bitcoiniacs-atm-locator'); ?>
    </button>
    <button class="selector-tab disabled" data-tab="city">
      <?php _e('City', 'bitcoiniacs-atm-locator'); ?>
    </button>
    <button class="selector-tab disabled" data-tab="atms">
      <?php _e('ATMs', 'bitcoiniacs-atm-locator'); ?>
    </button>
  </div>
  
  <div class="visual-selector-content">
    <!-- Country Selection -->
    <div class="selector-panel active" id="country-panel">
      <div class="country-grid">
        <div class="country-card" data-country="CA" data-center="[56.1304, -106.3468]" data-zoom="4">
          <div class="country-flag">🇨🇦</div>
          <h4><?php _e('Canada', 'bitcoiniacs-atm-locator'); ?></h4>
          <p class="country-stats">
            <span class="atm-count" data-country="CA">-</span> <?php _e('ATMs', 'bitcoiniacs-atm-locator'); ?>
          </p>
        </div>
        

        
        <div class="country-card" data-country="PH" data-center="[12.8797, 121.7740]" data-zoom="6">
          <div class="country-flag">🇵🇭</div>
          <h4><?php _e('Philippines', 'bitcoiniacs-atm-locator'); ?></h4>
          <p class="country-stats">
            <span class="atm-count" data-country="PH">-</span> <?php _e('ATMs', 'bitcoiniacs-atm-locator'); ?>
          </p>
        </div>
      </div>
    </div>
    
    <!-- Region Selection -->
    <div class="selector-panel" id="region-panel">
      <div class="bitcoiniacs-nav-path">
        <span class="bitcoiniacs-nav-item">
          <button class="bitcoiniacs-nav-link" data-tab="country">
            <?php _e('Countries', 'bitcoiniacs-atm-locator'); ?>
          </button>
        </span>
        <span class="bitcoiniacs-nav-sep">›</span>
        <span class="bitcoiniacs-nav-item current-country"><?php _e('Select Country', 'bitcoiniacs-atm-locator'); ?></span>
      </div>
      
      <div class="region-grid" id="region-grid">
        <!-- Regions will be populated dynamically -->
      </div>
    </div>
    
    <!-- City Selection -->
    <div class="selector-panel" id="city-panel">
      <div class="bitcoiniacs-nav-path">
        <span class="bitcoiniacs-nav-item">
          <button class="bitcoiniacs-nav-link" data-tab="country">
            <?php _e('Countries', 'bitcoiniacs-atm-locator'); ?>
          </button>
        </span>
        <span class="bitcoiniacs-nav-sep">›</span>
        <span class="bitcoiniacs-nav-item">
          <button class="bitcoiniacs-nav-link" data-tab="region">
            <span class="current-country"><?php _e('Country', 'bitcoiniacs-atm-locator'); ?></span>
          </button>
        </span>
        <span class="bitcoiniacs-nav-sep">›</span>
        <span class="bitcoiniacs-nav-item current-region"><?php _e('Select Region', 'bitcoiniacs-atm-locator'); ?></span>
      </div>
      
      <div class="city-grid" id="city-grid">
        <!-- Cities will be populated dynamically -->
      </div>
    </div>

    <!-- ATM Locations -->
    <div class="selector-panel" id="atms-panel">
      <div class="bitcoiniacs-nav-path">
        <span class="bitcoiniacs-nav-item">
          <button class="bitcoiniacs-nav-link" data-tab="country">
            <?php _e('Countries', 'bitcoiniacs-atm-locator'); ?>
          </button>
        </span>
        <span class="bitcoiniacs-nav-sep">›</span>
        <span class="bitcoiniacs-nav-item">
          <button class="bitcoiniacs-nav-link" data-tab="region">
            <span class="current-country"><?php _e('Country', 'bitcoiniacs-atm-locator'); ?></span>
          </button>
        </span>
        <span class="bitcoiniacs-nav-sep">›</span>
        <span class="bitcoiniacs-nav-item">
          <button class="bitcoiniacs-nav-link" data-tab="city">
            <span class="current-region"><?php _e('Region', 'bitcoiniacs-atm-locator'); ?></span>
          </button>
        </span>
        <span class="bitcoiniacs-nav-sep">›</span>
        <span class="bitcoiniacs-nav-item">
          <button class="bitcoiniacs-nav-link" data-tab="city">
            <span class="current-city"><?php _e('Select City', 'bitcoiniacs-atm-locator'); ?></span>
          </button>
        </span>
        <span class="bitcoiniacs-nav-sep">›</span>
        <span class="bitcoiniacs-nav-item current-atms"><?php _e('ATM Locations', 'bitcoiniacs-atm-locator'); ?></span>
      </div>

      <div class="atms-grid" id="atms-grid">
        <!-- ATM locations will be populated dynamically -->
      </div>
    </div>
  </div>
</div>

<style>
/* Visual Selector Styles */
.bitcoiniacs-visual-selector {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.visual-selector-header {
  text-align: center;
  margin-bottom: 20px;
}

.visual-selector-header h3 {
  margin: 0 0 5px 0;
  color: #4ecdc4;
}

.visual-selector-header p {
  margin: 0;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.visual-selector-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.selector-tab {
  background: none;
  border: none;
  color: #b0b0b0;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.selector-tab.active,
.selector-tab:hover {
  color: #4ecdc4;
  border-bottom-color: #4ecdc4;
}

.selector-tab.disabled {
  color: #666;
  cursor: not-allowed;
  opacity: 0.5;
}

.selector-tab.disabled:hover {
  color: #666;
  border-bottom-color: transparent;
}

.selector-panel {
  display: none;
}

.selector-panel.active {
  display: block;
}

.country-grid,
.region-grid,
.city-grid,
.atms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.country-card,
.region-card,
.city-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.country-card:hover,
.region-card:hover,
.city-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.country-flag {
  font-size: 2rem;
  margin-bottom: 10px;
}

.country-card h4,
.region-card h4,
.city-card h4 {
  margin: 0 0 10px 0;
  color: #ffffff;
}

.country-stats,
.region-stats,
.city-stats {
  margin: 0;
  color: #96ceb4;
  font-size: 0.9rem;
}

.atm-count {
  font-weight: bold;
  color: #4ecdc4;
}

.bitcoiniacs-nav-path {
  margin-bottom: 8px;
  padding: 6px 0;
  color: #b0b0b0;
  font-size: 0.85rem;
  line-height: 1.2;
}

.bitcoiniacs-nav-item {
  display: inline;
}

.bitcoiniacs-nav-link {
  background: none;
  border: none;
  color: #4ecdc4;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: 0.85rem;
}

.bitcoiniacs-nav-sep {
  margin: 0 6px;
}

.region-card .region-icon,
.city-card .city-icon {
  font-size: 1.5rem;
  margin-bottom: 10px;
  display: block;
}

/* Loading state */
.selector-loading {
  text-align: center;
  padding: 15px;
  color: #4ecdc4;
}

.selector-loading .bitcoiniacs-spinner {
  display: inline-block;
  margin-right: 10px;
}

/* ATM Locations List */
.atm-locations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.atm-location-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  transition: all 0.3s ease;
}

.atm-location-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: #4ecdc4;
  transform: translateY(-2px);
}

.atm-location-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.atm-location-name {
  margin: 0;
  color: #4ecdc4;
  font-size: 1.1rem;
}

.atm-location-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.status-active {
  background: #46b450;
  color: white;
}

.status-inactive {
  background: #dc3232;
  color: white;
}

.status-maintenance {
  background: #ffb900;
  color: black;
}

.atm-location-address {
  color: #e0e0e0;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.atm-location-services {
  margin-bottom: 10px;
  font-size: 0.85rem;
}

.services-label {
  color: #b0b0b0;
  font-weight: bold;
}

.services-list {
  color: #4ecdc4;
}

.atm-location-center-btn {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.atm-location-center-btn:hover {
  background: linear-gradient(45deg, #44a08d, #4ecdc4);
  transform: scale(1.05);
}

/* Responsive */
@media (max-width: 768px) {
  .country-grid,
  .region-grid,
  .city-grid,
  .atms-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }
  
  .country-card,
  .region-card,
  .city-card {
    padding: 15px;
  }
  
  .visual-selector-tabs {
    flex-wrap: wrap;
  }
  
  .selector-tab {
    padding: 8px 15px;
    font-size: 0.9rem;
  }
}
</style>

<script>
jQuery(document).ready(function($) {
  const mapId = '<?php echo esc_js($map_id); ?>';
  const visualSelector = new BitcoiniacsVisualSelector(mapId);
  visualSelector.init();
});
</script>
